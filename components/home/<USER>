"use client";
import { useState, useEffect } from 'react';
import Image from 'next/image';

interface TeamMember {
  id: string;
  name: string;
  role: string;
  description: string;
  image: string;
  specialties: string[];
  experience: string;
}

const teamMembers: TeamMember[] = [
  {
    id: '1',
    name: 'Dr. <PERSON>',
    role: 'AI Research Director',
    description: 'Leading breakthrough research in neural networks and machine learning algorithms.',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face',
    specialties: ['Deep Learning', 'Computer Vision', 'NLP'],
    experience: '8+ years'
  },
  {
    id: '2',
    name: '<PERSON>',
    role: 'Senior ML Engineer',
    description: 'Architecting scalable AI solutions that transform business operations.',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
    specialties: ['MLOps', 'Cloud AI', 'Data Engineering'],
    experience: '6+ years'
  },
  {
    id: '3',
    name: '<PERSON><PERSON>',
    role: 'AI Product Manager',
    description: 'Bridging the gap between cutting-edge AI research and real-world applications.',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
    specialties: ['Product Strategy', 'AI Ethics', 'User Experience'],
    experience: '5+ years'
  }
];

export default function TeamSection() {
  const [activeCard, setActiveCard] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    const section = document.getElementById('team-section');
    if (section) {
      observer.observe(section);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section id="team-section" className="team-section">
      <div className="neural-pattern"></div>
      <div className="container">
        <div className={`section-header ${isVisible ? 'animate-in' : ''}`}>
          <span className="section-subtitle">Meet Our Experts</span>
          <h2 className="section-title">The dream team of AI innovation</h2>
          <p className="section-description">
            Our passionate team of AI researchers, engineers, and visionaries are dedicated to 
            pushing the boundaries of artificial intelligence and creating solutions that shape the future.
          </p>
        </div>

        <div className={`team-grid ${isVisible ? 'animate-in' : ''}`}>
          {teamMembers.map((member, index) => (
            <div
              key={member.id}
              className={`team-card ${activeCard === member.id ? 'active' : ''}`}
              onMouseEnter={() => setActiveCard(member.id)}
              onMouseLeave={() => setActiveCard(null)}
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <div className="card-background"></div>
              <div className="card-glow"></div>
              
              <div className="member-image-container">
                <div className="member-image">
                  <Image
                    src={member.image}
                    alt={member.name}
                    width={200}
                    height={200}
                    className="member-photo"
                  />
                  <div className="image-overlay"></div>
                </div>
                <div className="ai-badge">
                  <i className="fas fa-brain"></i>
                  <span>AI Expert</span>
                </div>
              </div>

              <div className="member-info">
                <h3 className="member-name">{member.name}</h3>
                <p className="member-role">{member.role}</p>
                <p className="member-description">{member.description}</p>
                
                <div className="member-details">
                  <div className="experience-badge">
                    <i className="fas fa-clock"></i>
                    <span>{member.experience}</span>
                  </div>
                  
                  <div className="specialties">
                    {member.specialties.map((specialty, idx) => (
                      <span key={idx} className="specialty-tag">
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              <div className="card-footer">
                <div className="neural-connections">
                  <div className="connection-node"></div>
                  <div className="connection-line"></div>
                  <div className="connection-node"></div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className={`team-stats ${isVisible ? 'animate-in' : ''}`}>
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-users"></i>
            </div>
            <div className="stat-content">
              <span className="stat-number" data-count="25">25+</span>
              <span className="stat-label">AI Specialists</span>
            </div>
          </div>
          
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-award"></i>
            </div>
            <div className="stat-content">
              <span className="stat-number" data-count="50">50+</span>
              <span className="stat-label">AI Projects Delivered</span>
            </div>
          </div>
          
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-graduation-cap"></i>
            </div>
            <div className="stat-content">
              <span className="stat-number" data-count="15">15+</span>
              <span className="stat-label">PhD Researchers</span>
            </div>
          </div>
          
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-rocket"></i>
            </div>
            <div className="stat-content">
              <span className="stat-number" data-count="99">99%</span>
              <span className="stat-label">Innovation Rate</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
