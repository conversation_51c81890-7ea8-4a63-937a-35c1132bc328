"use client";
import { useEffect } from "react";
import Head from "next/head";

import Hero from "@/components/home/<USER>";
import ServicesPreview from "@/components/home/<USER>";
import AIShowcase from "@/components/home/<USER>";
import AboutPreview from "@/components/home/<USER>";
import TeamSection from "@/components/home/<USER>";
import Process from "@/components/home/<USER>";
import Testimonials from "@/components/home/<USER>";
import ContactPreview from "@/components/home/<USER>";
import { initAnimations } from "@/lib/animations";

declare global {
  interface Window {
    audioEnabled: boolean;
  }
}

export default function Home() {
  useEffect(() => {
    // Initialize animations after component mounts
    const timer = setTimeout(() => {
      initAnimations();
    }, 1000);
    
    // Add sound elements to the document if they don't exist
    if (typeof window !== 'undefined') {
      if (!document.getElementById('background-sound')) {
        const backgroundSound = document.createElement('audio');
        backgroundSound.id = 'background-sound';
        backgroundSound.loop = true;
        const source = document.createElement('source');
        source.src = '/assets/cosmos.mp3';
        source.type = 'audio/mpeg';
        backgroundSound.appendChild(source);
        document.body.appendChild(backgroundSound);
      }
      
      if (!document.getElementById('hover-sound')) {
        const hoverSound = document.createElement('audio');
        hoverSound.id = 'hover-sound';
        const source = document.createElement('source');
        source.src = '/assets/hover.mp3';
        source.type = 'audio/mpeg';
        hoverSound.appendChild(source);
        document.body.appendChild(hoverSound);
      }
      
      if (!document.getElementById('click-sound')) {
        const clickSound = document.createElement('audio');
        clickSound.id = 'click-sound';
        const source = document.createElement('source');
        source.src = '/assets/click.mp3';
        source.type = 'audio/mpeg';
        clickSound.appendChild(source);
        document.body.appendChild(clickSound);
      }
      
      // Set global audio state
      window.audioEnabled = false;
    }
    
    // Initialize animation counter for stats
    const initCounterAnimations = () => {
      const statNumbers = document.querySelectorAll('.stat-number');
      
      statNumbers.forEach(statNumber => {
        const target = parseInt(statNumber.getAttribute('data-count') || '0', 10);
        const element = statNumber as HTMLElement;
        let current = 0;
        const increment = Math.ceil(target / 50);
        const duration = 1500; // in milliseconds
        const stepTime = Math.floor(duration / (target / increment));
        
        const updateCounter = () => {
          current += increment;
          if (current > target) {
            current = target;
          }
          
          element.textContent = current.toString();
          
          if (current < target) {
            setTimeout(updateCounter, stepTime);
          }
        };
        
        // Start counter animation when element is in viewport
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              setTimeout(updateCounter, 300);
              observer.unobserve(entry.target);
            }
          });
        }, { threshold: 0.5 });
        
        observer.observe(element);
      });
    };
    
    // Initialize counter animations after a delay
    setTimeout(initCounterAnimations, 1500);
    
    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      <Head>
        <title>Qoverse - Innovative Software Solutions</title>
        <meta name="description" content="Qoverse is a leading software development company specializing in AI Solutions, Web & Mobile Development, and Data Analytics." />
      </Head>
      
      {/* Custom cursor elements */}
      {/* <div className="cursor-dot"></div> */}
      {/* <div className="cursor-outline"></div> */}
      
      <Hero />

      <ServicesPreview />

      <AIShowcase />

      <AboutPreview />

      <TeamSection />

      <Process />
      
      <Testimonials />
      
      <ContactPreview />
      
      {/* Sound Toggle */}
      <div className="sound-toggle" onClick={() => {
        if (typeof window !== 'undefined') {
          const bgSound = document.getElementById('background-sound') as HTMLAudioElement;
          const soundOn = document.querySelector('.sound-icon.sound-on');
          const soundOff = document.querySelector('.sound-icon.sound-off');
          
          if (bgSound && soundOn && soundOff) {
            window.audioEnabled = !window.audioEnabled;
            
            if (window.audioEnabled) {
              bgSound.play().catch(e => {});
              soundOn.classList.add('active');
              soundOff.classList.remove('active');
            } else {
              bgSound.pause();
              soundOn.classList.remove('active');
              soundOff.classList.add('active');
            }
          }
        }
      }}>
        <div className="sound-icon sound-on">
          <i className="fas fa-volume-up"></i>
        </div>
        <div className="sound-icon sound-off active">
          <i className="fas fa-volume-mute"></i>
        </div>
      </div>
      
      {/* Back to Top Button */}
      <div className="back-to-top" onClick={() => {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }}>
        <i className="fas fa-chevron-up"></i>
      </div>
    </>
  );
}