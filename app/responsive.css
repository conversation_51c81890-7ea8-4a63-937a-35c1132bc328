/* Responsive Styles */

/* Large Devices (Desktops between 992px and 1200px) */
@media (max-width: 1200px) and (min-width: 992px) {
  html {
    font-size: 60%;
  }

  #hero {
    min-height: 60vh;
    padding: calc(14rem + var(--header-height)) 0 5rem;
  }

  .hero-text {
    margin-top: 10rem;
  }

  .hero-text h1 {
    font-size: 3.8rem;
    line-height: 1.2;
  }

  .hero-text p {
    font-size: 1.6rem;
  }

  .hero-cta {
    margin-top: 4rem;
  }

  .cta-button {
    font-size: 1.3rem;
    padding: 0.8rem 1.8rem;
  }
}

/* Medium Devices (Tablets, 992px and below) */
@media (max-width: 992px) {
  html {
    font-size: 58%;
  }

  .container {
    padding: 0 1.5rem;
  }

  .nav-links {
    gap: 2rem;
  }

  .hero-highlights {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .highlight-card {
    padding: 1.5rem;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 4rem;
  }

  .about-image {
    order: -1;
  }

  .about-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .stat-item:last-child {
    grid-column: span 2;
  }
}

/* Small Devices (Mobile, 768px and below) */
@media (max-width: 768px) {
  html {
    font-size: 55%;
  }

  /* Header and Navigation */
  .nav-toggle {
    display: flex;
  }

  .nav-links {
    position: fixed;
    top: var(--header-height);
    left: 0;
    width: 100%;
    height: calc(100vh - var(--header-height));
    background: var(--primary-bg);
    flex-direction: column;
    justify-content: center;
    gap: 3rem;
    transform: translateX(100%);
    transition: transform 0.5s ease;
    z-index: 999;
  }

  .nav-links.active {
    transform: translateX(0);
  }

  .nav-link {
    font-size: 2rem;
    padding: 1rem;
  }

  .nav-link::after {
    display: none;
  }

  .dropdown-toggle {
    display: block;
  }

  .theme-toggle {
    position: absolute;
    top: 50%;
    right: 2rem;
    transform: translateY(-50%);
  }

  /* Hero Section */
  #hero {
    min-height: 80vh;
    padding: calc(4rem + var(--header-height)) 0 16rem; /* Increased bottom padding from 14rem to 16rem */
  }

  .hero-content {
    padding: 2rem 0;
    gap: 2rem;
  }

  .hero-text {
    max-width: 90%;
    margin: 0 auto;
    text-align: center;
  }

  .hero-text h1 {
    font-size: 3.6rem;
    line-height: 1.2;
  }

  .hero-text p {
    font-size: 1.4rem;
    margin-top: 1rem;
  }

  .hero-highlights {
    grid-template-columns: 1fr;
    max-width: 90%;
    margin: 2rem auto 0;
    /* Fallback */
    flex-direction: column;
    align-items: center;
  }

  .highlight-card {
    padding: 1.5rem;
    min-width: 0;
    flex-direction: row;
    align-items: center;
  }

  .hero-cta {
    justify-content: center;
    margin-top: 1.5rem;
  }

  .cta-button {
    padding: 0.8rem 1.8rem;
    font-size: 1.2rem;
  }

  /* Services */
  .services-showcase {
    grid-template-columns: 1fr;
  }

  /* Process */
  .process-timeline::before {
    left: 2rem;
  }

  .timeline-node {
    padding-left: 5rem;
  }

  .node-number {
    width: 4rem;
    height: 4rem;
    font-size: 1.8rem;
  }

  /* Testimonials */
  .testimonial-card {
    padding: 3rem;
  }

  /* About Stats */
  .about-stats {
    grid-template-columns: 1fr;
  }

  .stat-item:last-child {
    grid-column: auto;
  }
}

/* Extra Small Devices (Small Mobile, 576px and below) */
@media (max-width: 576px) {
  html {
    font-size: 50%;
  }

  .container {
    padding: 0 1rem;
  }

  /* Modern Team Section Mobile */
  .team-showcase-modern {
    gap: 2.5rem;
    margin-top: 3rem;
  }

  .team-member-card-modern {
    border-radius: 1.2rem;
  }

  .member-image-modern {
    height: 22rem;
  }

  .member-info-modern {
    padding: 2rem 1.5rem;
  }

  .member-info-modern h3 {
    font-size: 2rem;
  }

  .member-position-modern {
    font-size: 1.4rem;
  }

  .member-info-modern p {
    font-size: 1.4rem;
  }

  .member-badge {
    top: -1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    font-size: 1rem;
  }

  .member-skills {
    gap: 0.6rem;
    margin-top: 1.5rem;
  }

  .skill-tag {
    padding: 0.4rem 0.8rem;
    font-size: 1rem;
  }

  .social-link-modern {
    width: 4.5rem;
    height: 4.5rem;
    font-size: 1.8rem;
  }

  /* Hero Section */
  #hero {
    min-height: 90vh;
    padding: calc(7rem + var(--header-height)) 0 20rem; /* Increased bottom padding from 16rem to 20rem */
  }

  .hero-text h1 {
    font-size: 2.8rem;
    line-height: 1.2;
  }

  .hero-text p {
    font-size: 1.2rem;
    margin-top: 0.8rem;
  }

  .hero-highlights {
    max-width: 100%;
  }

  .highlight-card {
    padding: 1.2rem;
    flex-direction: row;
    align-items: center;
    gap: 1rem;
  }

  .highlight-icon {
    width: 4rem;
    height: 4rem;
    font-size: 1.8rem;
    flex-shrink: 0;
  }

  .highlight-text h3 {
    font-size: 1.6rem;
  }

  .highlight-text p {
    font-size: 1.3rem;
  }

  .hero-cta {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
  }

  .cta-button {
    width: 100%;
    max-width: 28rem;
    font-size: 1.1rem;
    padding: 0.7rem 1.6rem;
  }

  /* Services */
  .service-card {
    padding: 2rem;
  }

  .service-icon {
    width: 6rem;
    height: 6rem;
    font-size: 2.5rem;
  }

  /* About */
  .about-image {
    border-radius: 1rem;
  }

  /* Process */
  .node-content {
    padding: 2rem;
  }

  /* Testimonials */
  .testimonial-card {
    padding: 2rem;
  }

  /* Stats */
  .stat-number {
    font-size: 3.5rem;
  }

  .stat-symbol {
    font-size: 2.2rem;
  }
}

/* Very Small Devices (320px and below) */
@media (max-width: 320px) {
  #hero {
    min-height: 100vh;
    padding: calc(7rem + var(--header-height)) 0 22rem; /* Increased bottom padding from 18rem to 22rem */
  }

  .hero-text h1 {
    font-size: 2.4rem;
  }

  .hero-text p {
    font-size: 1.1rem;
  }

  .highlight-card {
    padding: 1rem;
  }

  .highlight-icon {
    width: 3.5rem;
    height: 3.5rem;
    font-size: 1.6rem;
  }

  .cta-button {
    padding: 0.7rem 1.5rem;
    font-size: 1rem;
  }
}

/* Viewports with Small Heights (e.g., 642px) */
@media (max-height: 800px) {
  #hero {
    min-height: 90vh;
    padding: calc(12rem + var(--header-height)) 0 22rem; /* Increased bottom padding from 18rem to 22rem */
  }

  .hero-text {
    margin-top: 8rem;
  }

  .hero-text h1 {
    font-size: 3.5rem;
    line-height: 1.2;
  }

  .hero-content {
    gap: 2rem;
  }

  .hero-cta {
    margin-top: 1.5rem;
  }

  .cta-button {
    font-size: 1.1rem;
    padding: 0.7rem 1.6rem;
  }
}
